package middleware

import (
	"context"
	"fmt"
	"loop/pkg/jwtx"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

const (
	// Redis键前缀
	RedeemLimitUserPrefix = "redeem_limit:user:"
	RedeemLimitIPPrefix   = "redeem_limit:ip:"

	// 默认限制配置
	DefaultUserDailyLimit  = 5  // 用户每日兑换限制
	DefaultIPDailyLimit    = 20 // IP每日兑换限制
	DefaultUserHourlyLimit = 2  // 用户每小时兑换限制
	DefaultIPHourlyLimit   = 10 // IP每小时兑换限制
)

// RedeemLimitConfig 兑换限制配置
type RedeemLimitConfig struct {
	UserDailyLimit  int
	IPDailyLimit    int
	UserHourlyLimit int
	IPHourlyLimit   int
}

// DefaultRedeemLimitConfig 默认配置
var DefaultRedeemLimitConfig = RedeemLimitConfig{
	UserDailyLimit:  DefaultUserDailyLimit,
	IPDailyLimit:    DefaultIPDailyLimit,
	UserHourlyLimit: DefaultUserHourlyLimit,
	IPHourlyLimit:   DefaultIPHourlyLimit,
}

// RedeemLimitMiddleware 兑换频率限制中间件
func RedeemLimitMiddleware(redisClient *redis.Client, config ...RedeemLimitConfig) gin.HandlerFunc {
	cfg := DefaultRedeemLimitConfig
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(c *gin.Context) {
		// 只对兑换接口进行限制
		if c.Request.URL.Path != "/api/v1/vip/exchange-code" {
			c.Next()
			return
		}

		uid := jwtx.GetUid(c)
		clientIP := c.ClientIP()
		now := time.Now()

		// 检查用户限制
		if uid != "" {
			if !checkUserLimit(redisClient, uid, now, cfg) {
				logrus.WithFields(logrus.Fields{
					"uid":       uid,
					"client_ip": clientIP,
					"action":    "redeem_limit_exceeded",
					"type":      "user",
				}).Warn("User redeem limit exceeded")

				c.JSON(http.StatusTooManyRequests, gin.H{
					"code": http.StatusTooManyRequests,
					"msg":  "兑换次数超出限制，请稍后再试",
				})
				c.Abort()
				return
			}
		}

		// 检查IP限制
		if !checkIPLimit(redisClient, clientIP, now, cfg) {
			logrus.WithFields(logrus.Fields{
				"uid":       uid,
				"client_ip": clientIP,
				"action":    "redeem_limit_exceeded",
				"type":      "ip",
			}).Warn("IP redeem limit exceeded")

			c.JSON(http.StatusTooManyRequests, gin.H{
				"code": http.StatusTooManyRequests,
				"msg":  "该IP兑换次数超出限制，请稍后再试",
			})
			c.Abort()
			return
		}

		c.Next()

		// 请求成功后增加计数
		if c.Writer.Status() == http.StatusOK {
			incrementRedeemCount(redisClient, uid, clientIP, now)
		}
	}
}

// checkUserLimit 检查用户限制
func checkUserLimit(redisClient *redis.Client, uid string, now time.Time, cfg RedeemLimitConfig) bool {
	ctx := context.Background()

	// 检查每日限制
	dailyKey := fmt.Sprintf("%s%s:daily:%s", RedeemLimitUserPrefix, uid, now.Format("2006-01-02"))
	dailyCount, err := redisClient.Get(ctx, dailyKey).Int()
	if err == nil && dailyCount >= cfg.UserDailyLimit {
		return false
	}

	// 检查每小时限制
	hourlyKey := fmt.Sprintf("%s%s:hourly:%s", RedeemLimitUserPrefix, uid, now.Format("2006-01-02:15"))
	hourlyCount, err := redisClient.Get(ctx, hourlyKey).Int()
	if err == nil && hourlyCount >= cfg.UserHourlyLimit {
		return false
	}

	return true
}

// checkIPLimit 检查IP限制
func checkIPLimit(redisClient *redis.Client, ip string, now time.Time, cfg RedeemLimitConfig) bool {
	ctx := context.Background()

	// 检查每日限制
	dailyKey := fmt.Sprintf("%s%s:daily:%s", RedeemLimitIPPrefix, ip, now.Format("2006-01-02"))
	dailyCount, err := redisClient.Get(ctx, dailyKey).Int()
	if err == nil && dailyCount >= cfg.IPDailyLimit {
		return false
	}

	// 检查每小时限制
	hourlyKey := fmt.Sprintf("%s%s:hourly:%s", RedeemLimitIPPrefix, ip, now.Format("2006-01-02:15"))
	hourlyCount, err := redisClient.Get(ctx, hourlyKey).Int()
	if err == nil && hourlyCount >= cfg.IPHourlyLimit {
		return false
	}

	return true
}

// incrementRedeemCount 增加兑换计数
func incrementRedeemCount(redisClient *redis.Client, uid, ip string, now time.Time) {
	ctx := context.Background()

	// 用户计数
	if uid != "" {
		// 每日计数
		dailyKey := fmt.Sprintf("%s%s:daily:%s", RedeemLimitUserPrefix, uid, now.Format("2006-01-02"))
		redisClient.Incr(ctx, dailyKey)
		redisClient.Expire(ctx, dailyKey, 25*time.Hour) // 稍微长一点确保不会提前过期

		// 每小时计数
		hourlyKey := fmt.Sprintf("%s%s:hourly:%s", RedeemLimitUserPrefix, uid, now.Format("2006-01-02:15"))
		redisClient.Incr(ctx, hourlyKey)
		redisClient.Expire(ctx, hourlyKey, 2*time.Hour)
	}

	// IP计数
	// 每日计数
	dailyKey := fmt.Sprintf("%s%s:daily:%s", RedeemLimitIPPrefix, ip, now.Format("2006-01-02"))
	redisClient.Incr(ctx, dailyKey)
	redisClient.Expire(ctx, dailyKey, 25*time.Hour)

	// 每小时计数
	hourlyKey := fmt.Sprintf("%s%s:hourly:%s", RedeemLimitIPPrefix, ip, now.Format("2006-01-02:15"))
	redisClient.Incr(ctx, hourlyKey)
	redisClient.Expire(ctx, hourlyKey, 2*time.Hour)
}

// GetRedeemLimitStatus 获取兑换限制状态
func GetRedeemLimitStatus(redisClient *redis.Client, uid, ip string, cfg RedeemLimitConfig) map[string]interface{} {
	ctx := context.Background()
	now := time.Now()

	status := make(map[string]interface{})

	if uid != "" {
		// 用户限制状态
		dailyKey := fmt.Sprintf("%s%s:daily:%s", RedeemLimitUserPrefix, uid, now.Format("2006-01-02"))
		dailyCount, _ := redisClient.Get(ctx, dailyKey).Int()

		hourlyKey := fmt.Sprintf("%s%s:hourly:%s", RedeemLimitUserPrefix, uid, now.Format("2006-01-02:15"))
		hourlyCount, _ := redisClient.Get(ctx, hourlyKey).Int()

		status["user"] = map[string]interface{}{
			"daily_used":       dailyCount,
			"daily_limit":      cfg.UserDailyLimit,
			"daily_remaining":  cfg.UserDailyLimit - dailyCount,
			"hourly_used":      hourlyCount,
			"hourly_limit":     cfg.UserHourlyLimit,
			"hourly_remaining": cfg.UserHourlyLimit - hourlyCount,
		}
	}

	// IP限制状态
	dailyKey := fmt.Sprintf("%s%s:daily:%s", RedeemLimitIPPrefix, ip, now.Format("2006-01-02"))
	dailyCount, _ := redisClient.Get(ctx, dailyKey).Int()

	hourlyKey := fmt.Sprintf("%s%s:hourly:%s", RedeemLimitIPPrefix, ip, now.Format("2006-01-02:15"))
	hourlyCount, _ := redisClient.Get(ctx, hourlyKey).Int()

	status["ip"] = map[string]interface{}{
		"daily_used":       dailyCount,
		"daily_limit":      cfg.IPDailyLimit,
		"daily_remaining":  cfg.IPDailyLimit - dailyCount,
		"hourly_used":      hourlyCount,
		"hourly_limit":     cfg.IPHourlyLimit,
		"hourly_remaining": cfg.IPHourlyLimit - hourlyCount,
	}

	return status
}
