name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点运行一次完整测试
    - cron: '0 2 * * *'

env:
  GO_VERSION: '1.21'
  MYSQL_VERSION: '8.0'
  REDIS_VERSION: '7.0'

jobs:
  # 代码质量检查
  lint:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Run go fmt
      run: |
        if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
          echo "Code is not formatted properly:"
          gofmt -s -l .
          exit 1
        fi

    - name: Run go vet
      run: go vet ./...

    - name: Install golangci-lint
      run: |
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2

    - name: Run golangci-lint
      run: golangci-lint run --timeout=5m

  # 单元测试
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      mysql:
        image: mysql:${{ env.MYSQL_VERSION }}
        env:
          MYSQL_ROOT_PASSWORD: 123456
          MYSQL_DATABASE: lsenglish_test
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:${{ env.REDIS_VERSION }}
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Wait for services
      run: |
        # 等待MySQL启动
        for i in {1..30}; do
          if mysqladmin ping -h127.0.0.1 -P3306 -uroot -p123456 --silent; then
            echo "MySQL is ready"
            break
          fi
          echo "Waiting for MySQL..."
          sleep 2
        done
        
        # 等待Redis启动
        for i in {1..30}; do
          if redis-cli -h 127.0.0.1 -p 6379 ping; then
            echo "Redis is ready"
            break
          fi
          echo "Waiting for Redis..."
          sleep 2
        done

    - name: Set up test database
      run: |
        mysql -h127.0.0.1 -P3306 -uroot -p123456 -e "CREATE DATABASE IF NOT EXISTS lsenglish_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        if [ -f test/config/database.sql ]; then
          mysql -h127.0.0.1 -P3306 -uroot -p123456 lsenglish_test < test/config/database.sql
        fi

    - name: Run unit tests
      env:
        TEST_DB_DSN: "root:123456@tcp(127.0.0.1:3306)/lsenglish_test?charset=utf8mb4&parseTime=True&loc=Local"
        TEST_REDIS_ADDR: "127.0.0.1:6379"
      run: |
        cd test
        make test-unit

    - name: Upload unit test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-results
        path: |
          test/reports/unit_test.log
          test/coverage/unit_coverage.out

  # 集成测试
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      mysql:
        image: mysql:${{ env.MYSQL_VERSION }}
        env:
          MYSQL_ROOT_PASSWORD: 123456
          MYSQL_DATABASE: lsenglish_test
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:${{ env.REDIS_VERSION }}
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Wait for services
      run: |
        # 等待MySQL启动
        for i in {1..30}; do
          if mysqladmin ping -h127.0.0.1 -P3306 -uroot -p123456 --silent; then
            echo "MySQL is ready"
            break
          fi
          echo "Waiting for MySQL..."
          sleep 2
        done
        
        # 等待Redis启动
        for i in {1..30}; do
          if redis-cli -h 127.0.0.1 -p 6379 ping; then
            echo "Redis is ready"
            break
          fi
          echo "Waiting for Redis..."
          sleep 2
        done

    - name: Set up test database
      run: |
        mysql -h127.0.0.1 -P3306 -uroot -p123456 -e "CREATE DATABASE IF NOT EXISTS lsenglish_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        if [ -f test/config/database.sql ]; then
          mysql -h127.0.0.1 -P3306 -uroot -p123456 lsenglish_test < test/config/database.sql
        fi

    - name: Run integration tests
      env:
        TEST_DB_DSN: "root:123456@tcp(127.0.0.1:3306)/lsenglish_test?charset=utf8mb4&parseTime=True&loc=Local"
        TEST_REDIS_ADDR: "127.0.0.1:6379"
      run: |
        cd test
        make test-integration

    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: |
          test/reports/integration_test.log
          test/coverage/integration_coverage.out

  # 模块测试矩阵
  module-tests:
    name: Module Tests
    runs-on: ubuntu-latest
    needs: lint
    strategy:
      matrix:
        module: [user, vip, benefit, plan, player, video]
      fail-fast: false
    
    services:
      mysql:
        image: mysql:${{ env.MYSQL_VERSION }}
        env:
          MYSQL_ROOT_PASSWORD: 123456
          MYSQL_DATABASE: lsenglish_test
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:${{ env.REDIS_VERSION }}
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Wait for services
      run: |
        # 等待MySQL启动
        for i in {1..30}; do
          if mysqladmin ping -h127.0.0.1 -P3306 -uroot -p123456 --silent; then
            echo "MySQL is ready"
            break
          fi
          echo "Waiting for MySQL..."
          sleep 2
        done
        
        # 等待Redis启动
        for i in {1..30}; do
          if redis-cli -h 127.0.0.1 -p 6379 ping; then
            echo "Redis is ready"
            break
          fi
          echo "Waiting for Redis..."
          sleep 2
        done

    - name: Set up test database
      run: |
        mysql -h127.0.0.1 -P3306 -uroot -p123456 -e "CREATE DATABASE IF NOT EXISTS lsenglish_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        if [ -f test/config/database.sql ]; then
          mysql -h127.0.0.1 -P3306 -uroot -p123456 lsenglish_test < test/config/database.sql
        fi

    - name: Run ${{ matrix.module }} module tests
      env:
        TEST_DB_DSN: "root:123456@tcp(127.0.0.1:3306)/lsenglish_test?charset=utf8mb4&parseTime=True&loc=Local"
        TEST_REDIS_ADDR: "127.0.0.1:6379"
      run: |
        cd test
        make test-${{ matrix.module }}

    - name: Upload ${{ matrix.module }} test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: ${{ matrix.module }}-test-results
        path: |
          test/reports/${{ matrix.module }}_test.log
          test/coverage/${{ matrix.module }}_coverage.out

  # 覆盖率报告
  coverage:
    name: Coverage Report
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, module-tests]
    if: always()

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Download all test results
      uses: actions/download-artifact@v3

    - name: Merge coverage reports
      run: |
        mkdir -p test/coverage
        echo "mode: atomic" > test/coverage/merged_coverage.out
        
        # 合并所有覆盖率文件
        find . -name "*_coverage.out" -exec tail -n +2 {} \; >> test/coverage/merged_coverage.out

    - name: Generate coverage report
      run: |
        mkdir -p test/reports
        go tool cover -html=test/coverage/merged_coverage.out -o test/reports/coverage.html
        go tool cover -func=test/coverage/merged_coverage.out > test/reports/coverage.txt
        
        # 显示覆盖率
        COVERAGE=$(go tool cover -func=test/coverage/merged_coverage.out | grep "total:" | awk '{print $3}')
        echo "Total Coverage: $COVERAGE"
        echo "COVERAGE=$COVERAGE" >> $GITHUB_ENV

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: test/coverage/merged_coverage.out
        flags: unittests
        name: codecov-umbrella

    - name: Comment coverage on PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const coverage = process.env.COVERAGE;
          const comment = `## 📊 Test Coverage Report
          
          **Total Coverage: ${coverage}**
          
          [View detailed coverage report](https://codecov.io/gh/${{ github.repository }}/commit/${{ github.sha }})
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

    - name: Upload final reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: coverage-report
        path: |
          test/reports/coverage.html
          test/reports/coverage.txt
          test/coverage/merged_coverage.out
