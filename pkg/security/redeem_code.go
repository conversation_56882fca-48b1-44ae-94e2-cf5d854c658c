package security

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base32"
	"encoding/hex"
	"fmt"
	"strings"
	"time"
)

const (
	// 兑换码长度配置
	CodeLength     = 16  // 兑换码长度
	ChecksumLength = 4   // 校验码长度
	
	// 字符集（去除容易混淆的字符）
	charset = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"
)

// RedeemCodeGenerator 兑换码生成器
type RedeemCodeGenerator struct {
	secretKey []byte
}

// NewRedeemCodeGenerator 创建兑换码生成器
func NewRedeemCodeGenerator(secretKey string) *RedeemCodeGenerator {
	return &RedeemCodeGenerator{
		secretKey: []byte(secretKey),
	}
}

// GenerateSecureCode 生成安全的兑换码
func (g *RedeemCodeGenerator) GenerateSecureCode() (string, string, error) {
	// 生成随机字节
	randomBytes := make([]byte, 12)
	if _, err := rand.Read(randomBytes); err != nil {
		return "", "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	
	// 添加时间戳（防止重复）
	timestamp := time.Now().UnixNano()
	timestampBytes := make([]byte, 8)
	for i := 0; i < 8; i++ {
		timestampBytes[i] = byte(timestamp >> (i * 8))
	}
	
	// 合并随机字节和时间戳
	combined := append(randomBytes, timestampBytes[:4]...)
	
	// 使用Base32编码（去除填充）
	encoded := base32.StdEncoding.WithPadding(base32.NoPadding).EncodeToString(combined)
	
	// 转换为自定义字符集
	code := g.convertToCustomCharset(encoded)
	
	// 限制长度
	if len(code) > CodeLength {
		code = code[:CodeLength]
	}
	
	// 生成校验码
	checksum := g.generateChecksum(code)
	
	// 格式化最终兑换码（添加分隔符提高可读性）
	finalCode := g.formatCode(code + checksum)
	
	// 生成哈希值用于数据库存储
	hash := g.generateHash(finalCode)
	
	return finalCode, hash, nil
}

// convertToCustomCharset 转换为自定义字符集
func (g *RedeemCodeGenerator) convertToCustomCharset(input string) string {
	var result strings.Builder
	charsetLen := len(charset)
	
	for _, char := range input {
		// 将字符转换为数字索引
		index := int(char) % charsetLen
		result.WriteByte(charset[index])
	}
	
	return result.String()
}

// generateChecksum 生成校验码
func (g *RedeemCodeGenerator) generateChecksum(code string) string {
	h := hmac.New(sha256.New, g.secretKey)
	h.Write([]byte(code))
	hash := h.Sum(nil)
	
	// 取前2个字节转换为自定义字符集
	checksum := ""
	for i := 0; i < ChecksumLength/2; i++ {
		index := int(hash[i]) % len(charset)
		checksum += string(charset[index])
	}
	
	// 确保校验码长度
	for len(checksum) < ChecksumLength {
		index := int(hash[len(checksum)]) % len(charset)
		checksum += string(charset[index])
	}
	
	return checksum[:ChecksumLength]
}

// formatCode 格式化兑换码（添加分隔符）
func (g *RedeemCodeGenerator) formatCode(code string) string {
	// 每4个字符添加一个分隔符
	var formatted strings.Builder
	for i, char := range code {
		if i > 0 && i%4 == 0 {
			formatted.WriteString("-")
		}
		formatted.WriteRune(char)
	}
	return formatted.String()
}

// generateHash 生成兑换码哈希值
func (g *RedeemCodeGenerator) generateHash(code string) string {
	h := hmac.New(sha256.New, g.secretKey)
	h.Write([]byte(code))
	return hex.EncodeToString(h.Sum(nil))
}

// ValidateCode 验证兑换码格式和校验码
func (g *RedeemCodeGenerator) ValidateCode(code string) bool {
	// 移除分隔符
	cleanCode := strings.ReplaceAll(code, "-", "")
	
	// 检查长度
	if len(cleanCode) != CodeLength+ChecksumLength {
		return false
	}
	
	// 分离主码和校验码
	mainCode := cleanCode[:CodeLength]
	providedChecksum := cleanCode[CodeLength:]
	
	// 验证校验码
	expectedChecksum := g.generateChecksum(mainCode)
	
	return hmac.Equal([]byte(providedChecksum), []byte(expectedChecksum))
}

// GenerateHash 为现有兑换码生成哈希值
func (g *RedeemCodeGenerator) GenerateHash(code string) string {
	return g.generateHash(code)
}

// GenerateBatchCodes 批量生成兑换码
func (g *RedeemCodeGenerator) GenerateBatchCodes(count int) ([]CodePair, error) {
	codes := make([]CodePair, 0, count)
	codeSet := make(map[string]bool) // 防重复
	
	for len(codes) < count {
		code, hash, err := g.GenerateSecureCode()
		if err != nil {
			return nil, err
		}
		
		// 检查重复
		if !codeSet[code] {
			codeSet[code] = true
			codes = append(codes, CodePair{
				Code: code,
				Hash: hash,
			})
		}
	}
	
	return codes, nil
}

// CodePair 兑换码对
type CodePair struct {
	Code string
	Hash string
}
