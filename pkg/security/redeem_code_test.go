package security

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRedeemCodeGeneratorBasic 测试兑换码生成器基本功能
func TestRedeemCodeGeneratorBasic(t *testing.T) {
	generator := NewRedeemCodeGenerator("test-secret-key")

	// 测试生成单个兑换码
	code, hash, err := generator.GenerateSecureCode()
	require.NoError(t, err)
	assert.NotEmpty(t, code)
	assert.NotEmpty(t, hash)

	// 验证兑换码格式
	assert.True(t, generator.ValidateCode(code))

	// 测试无效兑换码
	assert.False(t, generator.ValidateCode("INVALID-CODE"))
	assert.False(t, generator.ValidateCode(""))
	assert.False(t, generator.ValidateCode("TOO-SHORT"))
}

// TestRedeemCodeValidation 测试兑换码验证
func TestRedeemCodeValidation(t *testing.T) {
	generator := NewRedeemCodeGenerator("test-secret-key")

	// 生成有效兑换码
	code, _, err := generator.GenerateSecureCode()
	require.NoError(t, err)

	// 测试有效兑换码
	assert.True(t, generator.ValidateCode(code))

	// 测试各种无效兑换码
	invalidCodes := []string{
		"INVALID-CODE-1234",
		"",
		"TOO-SHORT",
		"ABCD-EFGH-IJKL",                // 太短
		"ABCD-EFGH-IJKL-MNOP-QRST-UVWX", // 太长
		"abcd-efgh-ijkl-mnop",           // 小写字母
		"1234-5678-9012-3456",           // 纯数字
	}

	for _, invalidCode := range invalidCodes {
		assert.False(t, generator.ValidateCode(invalidCode), "Code should be invalid: %s", invalidCode)
	}
}

// TestBatchCodeGeneration 测试批量生成兑换码
func TestBatchCodeGeneration(t *testing.T) {
	generator := NewRedeemCodeGenerator("test-secret-key")

	count := 10
	codes, err := generator.GenerateBatchCodes(count)
	require.NoError(t, err)

	// 验证数量
	assert.Equal(t, count, len(codes))

	// 验证唯一性
	codeSet := make(map[string]bool)
	for _, pair := range codes {
		assert.False(t, codeSet[pair.Code], "Duplicate code found: %s", pair.Code)
		codeSet[pair.Code] = true

		// 验证每个兑换码都有效
		assert.True(t, generator.ValidateCode(pair.Code))
		assert.NotEmpty(t, pair.Hash)
	}
}

// TestCodeFormat 测试兑换码格式
func TestCodeFormat(t *testing.T) {
	generator := NewRedeemCodeGenerator("test-secret-key")

	code, _, err := generator.GenerateSecureCode()
	require.NoError(t, err)

	// 验证格式特征
	assert.Contains(t, code, "-", "Code should contain separators")
	assert.True(t, len(code) > 16, "Code should be longer than 16 characters")
	assert.True(t, len(code) < 30, "Code should be shorter than 30 characters")

	// 验证字符集（应该只包含大写字母和数字，以及分隔符）
	for _, char := range code {
		if char != '-' {
			assert.True(t,
				(char >= 'A' && char <= 'Z') || (char >= '2' && char <= '9'),
				"Code should only contain uppercase letters and digits 2-9, found: %c", char)
		}
	}
}

// TestDifferentSecretKeys 测试不同密钥生成的兑换码
func TestDifferentSecretKeys(t *testing.T) {
	generator1 := NewRedeemCodeGenerator("secret-key-1")
	generator2 := NewRedeemCodeGenerator("secret-key-2")

	code1, hash1, err1 := generator1.GenerateSecureCode()
	require.NoError(t, err1)

	code2, hash2, err2 := generator2.GenerateSecureCode()
	require.NoError(t, err2)

	// 不同密钥生成的兑换码应该不同
	assert.NotEqual(t, code1, code2)
	assert.NotEqual(t, hash1, hash2)

	// 每个生成器只能验证自己生成的兑换码
	assert.True(t, generator1.ValidateCode(code1))
	assert.False(t, generator1.ValidateCode(code2))

	assert.True(t, generator2.ValidateCode(code2))
	assert.False(t, generator2.ValidateCode(code1))
}

// TestHashGeneration 测试哈希生成
func TestHashGeneration(t *testing.T) {
	generator := NewRedeemCodeGenerator("test-secret-key")

	code, hash1, err := generator.GenerateSecureCode()
	require.NoError(t, err)

	// 为同一个兑换码生成哈希应该得到相同结果
	hash2 := generator.GenerateHash(code)
	assert.Equal(t, hash1, hash2)

	// 不同兑换码应该有不同哈希
	code2, hash3, err := generator.GenerateSecureCode()
	require.NoError(t, err)
	assert.NotEqual(t, hash1, hash3)
	assert.NotEqual(t, code, code2)
}

// TestLargeScaleBatchGeneration 测试大规模批量生成
func TestLargeScaleBatchGeneration(t *testing.T) {
	generator := NewRedeemCodeGenerator("test-secret-key")

	// 测试生成较大数量的兑换码
	count := 100
	codes, err := generator.GenerateBatchCodes(count)
	require.NoError(t, err)

	assert.Equal(t, count, len(codes))

	// 验证所有兑换码都是唯一的
	codeSet := make(map[string]bool)
	hashSet := make(map[string]bool)

	for _, pair := range codes {
		assert.False(t, codeSet[pair.Code], "Duplicate code found")
		assert.False(t, hashSet[pair.Hash], "Duplicate hash found")

		codeSet[pair.Code] = true
		hashSet[pair.Hash] = true

		assert.True(t, generator.ValidateCode(pair.Code))
	}
}

// TestEdgeCases 测试边界情况
func TestEdgeCases(t *testing.T) {
	// 测试空密钥
	emptyKeyGenerator := NewRedeemCodeGenerator("")
	code, _, err := emptyKeyGenerator.GenerateSecureCode()
	require.NoError(t, err)
	assert.NotEmpty(t, code)

	// 测试很长的密钥
	longKey := "this-is-a-very-long-secret-key-that-should-still-work-properly-for-generating-secure-redeem-codes"
	longKeyGenerator := NewRedeemCodeGenerator(longKey)
	code2, _, err := longKeyGenerator.GenerateSecureCode()
	require.NoError(t, err)
	assert.NotEmpty(t, code2)

	// 测试特殊字符密钥
	specialKeyGenerator := NewRedeemCodeGenerator("!@#$%^&*()_+-=[]{}|;:,.<>?")
	code3, _, err := specialKeyGenerator.GenerateSecureCode()
	require.NoError(t, err)
	assert.NotEmpty(t, code3)
}

// BenchmarkCodeGeneration 性能测试
func BenchmarkCodeGeneration(b *testing.B) {
	generator := NewRedeemCodeGenerator("test-secret-key")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := generator.GenerateSecureCode()
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkCodeValidation 验证性能测试
func BenchmarkCodeValidation(b *testing.B) {
	generator := NewRedeemCodeGenerator("test-secret-key")

	// 预生成一些兑换码
	codes := make([]string, 100)
	for i := 0; i < 100; i++ {
		code, _, err := generator.GenerateSecureCode()
		if err != nil {
			b.Fatal(err)
		}
		codes[i] = code
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		code := codes[i%100]
		generator.ValidateCode(code)
	}
}
