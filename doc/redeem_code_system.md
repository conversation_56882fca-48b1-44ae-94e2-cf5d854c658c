# 兑换码系统使用说明

## 概述

新的兑换码系统提供了更安全、更灵活的兑换码管理功能，支持多种兑换类型、频率限制、操作日志等特性。

## 主要特性

### 1. 安全性增强
- **强随机算法生成**：使用加密安全的随机数生成器
- **校验码机制**：每个兑换码包含HMAC校验码，防止伪造
- **哈希存储**：数据库存储兑换码的哈希值，增强安全性
- **格式验证**：严格的兑换码格式验证

### 2. 多种兑换类型支持
- **VIP会员**：传统的VIP天数和等级兑换
- **积分奖励**：给用户增加积分
- **优惠券**：发放各种优惠券
- **内容解锁**：解锁特定内容或功能

### 3. 频率限制
- **用户限制**：每日/每小时兑换次数限制
- **IP限制**：防止单个IP恶意刷取
- **可配置**：通过配置文件灵活调整限制参数

### 4. 完整的日志系统
- **操作记录**：记录所有兑换操作
- **审计追踪**：包含用户、IP、时间等详细信息
- **错误日志**：记录失败原因便于排查

## API接口

### 1. 创建单个兑换码
```http
POST /api/v1/vip/redeem-code
Content-Type: application/json

{
  "redeemType": "vip",
  "days": 30,
  "vipLevel": 100,
  "maxUseCount": 1,
  "expiresAt": 1735689600,
  "description": "30天PRO会员"
}
```

### 2. 批量创建兑换码
```http
POST /api/v1/vip/redeem-codes/batch
Content-Type: application/json

{
  "redeemType": "vip",
  "days": 7,
  "vipLevel": 1,
  "maxUseCount": 1,
  "expiresAt": 1735689600,
  "description": "7天体验会员",
  "count": 100
}
```

### 3. 兑换码使用
```http
POST /api/v1/vip/exchange-code
Content-Type: application/json

{
  "code": "ABCD-EFGH-IJKL-MNOP"
}
```

### 4. 禁用兑换码
```http
POST /api/v1/vip/redeem-code/disable
Content-Type: application/json

{
  "code": "ABCD-EFGH-IJKL-MNOP",
  "reason": "活动结束"
}
```

## 兑换类型配置

### VIP会员兑换
```json
{
  "redeemType": "vip",
  "days": 30,
  "vipLevel": 100,
  "maxUseCount": 1
}
```

### 积分奖励兑换
```json
{
  "redeemType": "points",
  "rewardConfig": {
    "points": 1000,
    "bonus": "welcome_gift"
  },
  "maxUseCount": 1
}
```

### 优惠券兑换
```json
{
  "redeemType": "coupon",
  "rewardConfig": {
    "couponType": "discount",
    "value": 50,
    "validDays": 30
  },
  "maxUseCount": 1
}
```

### 内容解锁兑换
```json
{
  "redeemType": "content",
  "rewardConfig": {
    "contentIds": ["content_1", "content_2"],
    "unlockDays": 90
  },
  "maxUseCount": 1
}
```

## 配置说明

在配置文件中添加以下配置：

```yaml
RedeemCode:
  # 兑换码生成密钥（生产环境必须修改）
  SecretKey: "your-super-secret-key"
  
  # 频率限制配置
  UserDailyLimit: 5    # 用户每日兑换限制
  IPDailyLimit: 20     # IP每日兑换限制
  UserHourlyLimit: 2   # 用户每小时兑换限制
  IPHourlyLimit: 10    # IP每小时兑换限制
```

## 数据库表结构

### promotion_codes 表
- `code`: 兑换码（加密格式）
- `code_hash`: 兑换码哈希值
- `status`: 状态（0未使用，1已使用，2已过期，3已禁用）
- `redeem_type`: 兑换类型
- `reward_config`: 奖励配置（JSON格式）
- `max_use_count`: 最大使用次数
- `used_count`: 已使用次数
- `expires_at`: 过期时间
- `batch_id`: 批次ID

### redeem_logs 表
- `code`: 兑换码
- `uid`: 用户ID
- `action`: 操作类型
- `status`: 操作结果
- `client_ip`: 客户端IP
- `user_agent`: 用户代理
- `timestamp`: 操作时间

## 安全建议

1. **密钥管理**：生产环境必须使用强随机密钥
2. **HTTPS**：所有兑换操作必须使用HTTPS
3. **频率限制**：根据业务需求调整频率限制参数
4. **日志监控**：定期检查兑换日志，发现异常及时处理
5. **过期管理**：定期清理过期兑换码和日志

## 扩展开发

要添加新的兑换类型，需要：

1. 在 `model/vip.go` 中添加新的兑换类型常量
2. 在 `processRedeemReward` 方法中添加处理逻辑
3. 实现对应的奖励处理方法

示例：
```go
const RedeemTypeCustom = "custom"

func (r *VipModel) processCustomReward(txDb *dbx.DBExtension, redeemCode *PromotionCode, uid string) error {
    // 实现自定义奖励逻辑
    return nil
}
```
