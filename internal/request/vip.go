package request

type CreatePayOrderReq struct {
	ProductId uint `form:"productId" json:"productId" binding:"required"` //商品ID
}

type ExchangeCodeReq struct {
	Uid  string `form:"uid" json:"uid"`
	Code string `form:"code" json:"code" binding:"required"`
}

type AddPromotionCodeReq struct {
	Days     int `form:"days" json:"days" binding:"required"`
	VipLevel int `form:"vipLevel" json:"vipLevel" binding:"required"`
}

// CreateRedeemCodeReq 创建兑换码请求
type CreateRedeemCodeReq struct {
	RedeemType   string                 `form:"redeemType" json:"redeemType" binding:"required"`
	RewardConfig map[string]interface{} `form:"rewardConfig" json:"rewardConfig"`
	Days         int                    `form:"days" json:"days"`
	VipLevel     int                    `form:"vipLevel" json:"vipLevel"`
	MaxUseCount  int                    `form:"maxUseCount" json:"maxUseCount" binding:"required"`
	ExpiresAt    int64                  `form:"expiresAt" json:"expiresAt"`
	Description  string                 `form:"description" json:"description"`
}

// CreateBatchRedeemCodeReq 批量创建兑换码请求
type CreateBatchRedeemCodeReq struct {
	CreateRedeemCodeReq
	Count int `form:"count" json:"count" binding:"required,min=1,max=1000"`
}

// DisableRedeemCodeReq 禁用兑换码请求
type DisableRedeemCodeReq struct {
	Code   string `form:"code" json:"code" binding:"required"`
	Reason string `form:"reason" json:"reason"`
}

// GetRedeemLimitStatusReq 获取兑换限制状态请求
type GetRedeemLimitStatusReq struct {
	Uid string `form:"uid" json:"uid"`
}
