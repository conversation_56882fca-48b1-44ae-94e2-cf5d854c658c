package config

import (
	"time"
)

type Config struct {
	Env        string `yaml:"Env"`        // 环境：prod、dev
	BaseUrl    string `yaml:"BaseUrl"`    // base url
	Port       string `yaml:"Port"`       // 端口
	LogPath    string `yaml:"LogPath"`    // 日志文件
	IpDataPath string `yaml:"IpDataPath"` // IP数据文件

	Server struct {
		RunMode      string        `yaml:"RunMode"`
		HttpPort     int           `yaml:"HttpPort"`
		ReadTimeout  time.Duration `yaml:"ReadTimeout"`
		WriteTimeout time.Duration `yaml:"WriteTimeout"`
	} `yaml:"Server"`

	AI struct {
		MaxResourcesPerBatch int `yaml:"MaxResourcesPerBatch"` // 每批处理的最大资源数量
	} `yaml:"AI"`

	RedisConfig struct {
		Host         string `yaml:"Host"`
		Password     string `yaml:"Password"`
		MaxIdle      int    `yaml:"MaxIdle"`
		MaxActive    int    `yaml:"MaxActive"`
		IdleTimeout  int    `yaml:"IdleTimeout"`
		DialTimeout  int    `yaml:"DialTimeout"`
		ReadTimeout  int    `yaml:"ReadTimeout"`
		WriteTimeout int    `yaml:"WriteTimeout"`
		PoolTimeout  int    `yaml:"PoolTimeout"`
	} `yaml:"Redis"`

	DbConfig struct {
		Url                    string `yaml:"Url"`
		MaxIdleConns           int    `yaml:"MaxIdleConns"`
		MaxOpenConns           int    `yaml:"MaxOpenConns"`
		ConnMaxIdleTimeSeconds int    `yaml:"ConnMaxIdleTimeSeconds"`
		ConnMaxLifetimeSeconds int    `yaml:"ConnMaxLifetimeSeconds"`
	} `yaml:"DB"`

	Jwt struct {
		SignKey       string `yaml:"SignKey"`
		ExpireSeconds int    `yaml:"ExpireSeconds"`
	} `yaml:"Jwt"`

	JwtAdmin struct {
		SignKey       string `yaml:"SignKey"`
		ExpireSeconds int    `yaml:"ExpireSeconds"`
	} `yaml:"JwtAdmin"`

	Limit struct {
		EnableIpLimit   bool `yaml:"EnableIpLimit"`
		EnableUserLimit bool `yaml:"EnableUserLimit"`
	} `yaml:"Limit"`

	RedeemCode struct {
		SecretKey       string `yaml:"SecretKey"`       // 兑换码生成密钥
		UserDailyLimit  int    `yaml:"UserDailyLimit"`  // 用户每日兑换限制
		IPDailyLimit    int    `yaml:"IPDailyLimit"`    // IP每日兑换限制
		UserHourlyLimit int    `yaml:"UserHourlyLimit"` // 用户每小时兑换限制
		IPHourlyLimit   int    `yaml:"IPHourlyLimit"`   // IP每小时兑换限制
	} `yaml:"RedeemCode"`

	Setting struct {
		Language string `yaml:"Language"`

		AliyunOss struct {
			Host         string `yaml:"Host"`
			Bucket       string `yaml:"Bucket"`
			Endpoint     string `yaml:"Endpoint"`
			AccessId     string `yaml:"AccessId"`
			AccessSecret string `yaml:"AccessSecret"`
			Region       string `yaml:"Region"`
		} `yaml:"AliyunOss"`
	} `yaml:"Setting"`
}
