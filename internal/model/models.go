package model

import (
	"loop/pkg/util"
	"time"

	"gorm.io/gorm"
)

type Model struct {
	Id        string    `gorm:"primarykey" json:"id" form:"id"`
	CreatedAt time.Time `json:"createat" form:"createat"`
	UpdatedAt time.Time `json:"updateat" form:"updateat"`
}

// BeforeCreate will set a Snowflake ID before creating a record.
func (base *Model) BeforeCreate(tx *gorm.DB) (err error) {
	base.Id = util.GenerateSnowflakeID()
	return
}

type ModelAutoId struct {
	Id        uint      `gorm:"primarykey" json:"id" form:"id"`
	CreatedAt time.Time `json:"createat" form:"createat"`
	UpdatedAt time.Time `json:"updateat" form:"updateat"`
}

var Models = []interface{}{
	// 系统表
	&SysUser{}, &SysRole{},
	//用户信息表
	&User{}, &UserPlayerConfig{}, &WatchHistory{}, &DeletedUser{},
	//播放器相关表
	&UserSubtitleRelations{}, &UserLocalResource{}, &Note{}, &NoteCollectRelations{}, &UserRemoteResourceRelations{},
	&SpeechEvaluation{},
	//资源表
	&Series{}, &SeriesRelation{}, &Resource{}, &ResourceRelation{}, &CategoryType{}, &Category{},
	&CategorySeriesRelations{}, &CategoryResourceRelations{}, &SeriesResourceRelations{}, &FeaturedContent{},
	// 数据中心表
	&DataEpisode{}, &DataEpisodeEach{},
	// 支付相关表
	&VIP{}, &UserVIPRelations{}, &UserVIPFlow{}, &TradeProduct{}, &UserPurchaseOrder{}, &UserSubscription{}, &UserSubscriptionLog{}, &PromotionCode{}, &RedeemLog{},
	// 权益相关表
	&BenefitGroup{}, &Benefit{}, &UserBenefit{}, &VipBenefit{}, &UserBenefitLog{},
	// 学习计划相关表
	&UserQuestionnaire{}, &LearningPlan{}, &LearningPlanStage{}, &LearningPlanWeek{}, &LearningPlanDay{},
	// 用户反馈表
	&UserFeedback{},
}

// 本地笔记收藏表
type NoteCollectRelations struct {
	ModelAutoId
	Uid         string `gorm:"not null;index:idx_user_watch,sort:desc,priority:1"`
	LocalNoteId string `gorm:"type:varchar(20)"`
}

func (NoteCollectRelations) TableName() string {
	return "note_collect_relations"
}
