package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"loop/internal/config"
	"loop/pkg/dbx"
	"loop/pkg/security"
	"loop/pkg/timex"
	"loop/pkg/util"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/apple"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

// 支付相关的时间戳都为毫秒
const (
	TradeOrderStatusWaitPay   = 1
	TradeOrderStatusPayFinish = 2
	TradeOrderStatusPayFailed = 3

	UserSubscriptionStatusContract    = 1 //签约
	UserSubscriptionStatusTermination = 2 //解约

	// 订阅操作：1-签约，2-续约，3-解约,4-重新订阅,5-升级，6-降级
	UserSubscriptionOperationContract    = 1
	UserSubscriptionOperationRenewal     = 2
	UserSubscriptionOperationTermination = 3
	UserSubscriptionOperationReview      = 4
	UserSubscriptionOperationUpgrade     = 5
	UserSubscriptionOperationDowngrade   = 6

	// 会员等级
	VIP_LEVEL_NORMAL = 1
	VIP_LEVEL_PRO    = 100
	VIP_LEVEL_ULTITA = 1000
)

func NewVipModel(dbModel *DbModel, config *config.Config) *VipModel {
	return &VipModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type VipModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

// 会员
type VIP struct {
	ModelAutoId
	Name string `gorm:"size:64" comment:"名称"`
	// 会员等级与天数分离：等级决定权益，天数决定时长
	Level int `gorm:"default:1" comment:"会员等级"`
}

func (VIP) TableName() string {
	return "vips"
}

// 用户会员表
type UserVIPRelations struct {
	ModelAutoId
	Uid             string `gorm:"not null;index:idx_user_watch,priority:1"`
	IsVip           int    `gorm:"default:0" comment:"状态：0-非会员，1-是会员"`
	VipID           uint   `comment:"会员ID"`
	IsSubscription  int    `gorm:"default:0" comment:"是否为处于订阅中：0-否，1-是"`
	ExpireDate      string `gorm:"size:20" comment:"到期日期"`
	ExpireTimestamp int64  `comment:"到期日期时间戳,毫秒"`
}

func (UserVIPRelations) TableName() string {
	return "user_vip_relations"
}

// 用户会员流水表 会员的时间根据这个流水来统计
type UserVIPFlow struct {
	ModelAutoId
	Uid                string `gorm:"not null;index:idx_user_watch,priority:1"`
	Title              string `gorm:"type:text;comment:日志标题"`
	Type               int    `gorm:"default:1" comment:"产生流水的类型：1-订阅，2-兑换码"`
	Operation          int    `gorm:"default:1" comment:"操作：1-开通，2-关闭"`
	OperationTimestamp int64  `gorm:"type:bigint;default:0" comment:"开通/关闭时间戳"`
	Days               int    `gorm:"default:0" comment:"时间变更（天数）"`
	Terminal           int    `gorm:"default:0" comment:"终端：0-未知，1-Android，2-IOS"`
	BizID              string `gorm:"size:64" comment:"业务来源ID，如订单ID、兑换code"`
}

func (UserVIPFlow) TableName() string {
	return "user_vip_flows"
}

// 会员的商品表 和ios中的商品订阅对应上 不允许修改 是固定的
type TradeProduct struct {
	ModelAutoId
	Name           string  `gorm:"size:64" comment:"名称"`
	Type           int     `gorm:"default:1" comment:"商品类型：1-会员"`
	IsSubscription int     `gorm:"default:1" comment:"是否为订阅商品：0-否，1-是"`
	Price          float64 `gorm:"type:decimal(10,2)" comment:"实际价格"`
	OriginPrice    float64 `gorm:"type:decimal(10,2)" comment:"原价价格"`
	IosProductId   string  `gorm:"size:128;not null;comment:第三方支付平台商品编码,给ios用，固定的"` //类似com.merachmonthtest.www
	Currency       string  `gorm:"size:128;not null;comment:币种"`                    //三个字母的ISO 4217货币代码
	Terminal       int     `gorm:"default:1" comment:"终端：1-Android，2-IOS"`
	Days           int     `gorm:"default:0" comment:"商品对应的会员天数,-1为永久"`
	VipID          uint    `comment:"会员ID"`
}

func (TradeProduct) TableName() string {
	return "trade_products"
}

// 订单表
type UserPurchaseOrder struct {
	ModelAutoId
	OrderNo string `gorm:"size:64" comment:"订单编号"`
	//苹果为 originalTransactionId 首次先根据appAccountToken来判断绑定uid  后续根据originalTransactionId来获取uid
	OutTransactionId           string  `gorm:"size:64" comment:"外部交易号，apple 对应TransactionId"`
	AppleOriginalTransactionId string  `gorm:"size:64" comment:"苹果的针对每个用户绑定的唯一的交易ID，只要交易过就不会改变"`
	Uid                        string  `gorm:"not null;index:idx_user_watch,priority:1"`
	ProductID                  uint    `comment:"商品ID"`
	ProductName                string  `gorm:"size:64" comment:"名称"`
	ProductType                int     `gorm:"default:1" comment:"商品类型：1-会员"`
	UserSubscriptionID         uint    `comment:"订阅ID"`
	Currency                   string  `gorm:"size:128;not null;comment:币种"`
	Amount                     float64 `gorm:"type:decimal(10,2)" comment:"订单金额"`
	PaymentProvider            int     `gorm:"type:tinyint;not null" comment:"三方支付：1-苹果内购，2-微信 3-支付宝"`
	PaidTimestamp              int64   `comment:"支付时间戳"`
	Status                     int     `gorm:"default:1" comment:"订单状态：1-待支付，2-交易完成，3-交易失败"`
	PaymentFailureReason       string  `gorm:"type:text;comment:支付失败原因"` // 可存储具体错误码和描述
	FailureTimestamp           int64   `gorm:"comment:支付失败时间戳"`
}

func (UserPurchaseOrder) TableName() string {
	return "user_purchase_orders"
}

// 订阅 已订阅的，处于订单待支付的不会有这个信息 一个用户只有一个订阅  代表当前的订阅状态
// 订阅里的属性只和订阅挂钩 如果是送的会员，和这里无关，送的会员从流水里查询
type UserSubscription struct {
	ModelAutoId
	Uid                        string  `gorm:"not null;index:idx_user_watch,priority:1"`
	ProductID                  uint    `comment:"商品ID"`
	ProductName                string  `gorm:"size:64" comment:"名称"`
	PaymentProvider            int     `gorm:"type:tinyint;not null" comment:"三方支付：1-苹果内购，2-微信 3-支付宝"`
	OutTransactionId           string  `gorm:"size:64" comment:"外部交易号，apple 对应TransactionId"`
	AppleOriginalTransactionId string  `gorm:"size:64" comment:"苹果的针对每个用户绑定的唯一的交易ID，只要交易过就不会改变"`
	FirstCycleAmount           float64 `gorm:"type:decimal(10,2)" comment:"订阅第一个周期金额"`
	NextCycleAmount            float64 `gorm:"type:decimal(10,2)" comment:"订阅下一个周期金额"`
	NextPaidDate               string  `gorm:"size:20" comment:"下一次支付日期"`
	NextPaidTimestamp          int64   `comment:"下一次支付日期时间戳"`
	SignTimestamp              int64   `comment:"签约时间"`
	CancelTimestamp            int64   `comment:"解约时间"`
	Status                     int     `gorm:"default:1" comment:"订阅状态：1-签约，0-解约"`
	Currency                   string  `gorm:"size:128;not null;comment:币种"`
	Desc                       string  `gorm:"default:1" comment:"状态描述，比如注销"`
}

func (UserSubscription) TableName() string {
	return "user_subscriptions"
}

// 订阅日志表
type UserSubscriptionLog struct {
	ModelAutoId
	Uid                string `gorm:"not null;index:idx_user_watch,priority:1"`
	ProductID          uint   `gorm:"type:bigint;default:0"`
	ProductName        string `gorm:"size:64" comment:"名称"`
	PaymentProvider    int    `comment:"三方支付：1-苹果内购，2-微信 3-支付宝"`
	UserSubscriptionID uint   `gorm:"default:0" comment:"用户订阅ID"`
	Operation          int    `gorm:"default:1" comment:"订阅操作：1-签约，2-续约，3-解约,4-重新订阅,5-升级，6-降级"`
}

func (UserSubscriptionLog) TableName() string {
	return "user_subscription_logs"
}

// 兑换码类型枚举
const (
	RedeemTypeVIP     = "vip"     // VIP会员
	RedeemTypePoints  = "points"  // 积分
	RedeemTypeCoupon  = "coupon"  // 优惠券
	RedeemTypeContent = "content" // 内容解锁
)

// 兑换码状态枚举
const (
	RedeemStatusUnused   = 0 // 未使用
	RedeemStatusUsed     = 1 // 已使用
	RedeemStatusExpired  = 2 // 已过期
	RedeemStatusDisabled = 3 // 已禁用
)

// 改进的兑换码结构 - 支持多种兑换类型和安全特性
type PromotionCode struct {
	ModelAutoId
	// 基本信息
	Code     string `gorm:"size:32;uniqueIndex" json:"code" comment:"兑换码"`
	CodeHash string `gorm:"size:64;index" json:"-" comment:"兑换码哈希值，用于验证"`
	Status   int    `gorm:"default:0;index" json:"status" comment:"状态：0未使用，1已使用，2已过期，3已禁用"`

	// 兑换类型和奖励配置
	RedeemType   string `gorm:"size:20;index" json:"redeemType" comment:"兑换类型：vip,points,coupon,content"`
	RewardConfig string `gorm:"type:json" json:"rewardConfig" comment:"奖励配置JSON"`

	// VIP相关字段（向后兼容）
	Days     int `gorm:"default:0" json:"days" comment:"VIP天数"`
	VipLevel int `gorm:"default:1" json:"vipLevel" comment:"VIP等级"`

	// 使用限制
	MaxUseCount int   `gorm:"default:1" json:"maxUseCount" comment:"最大使用次数"`
	UsedCount   int   `gorm:"default:0" json:"usedCount" comment:"已使用次数"`
	ExpiresAt   int64 `json:"expiresAt" comment:"过期时间戳"`

	// 使用记录
	Uid               string `gorm:"index" json:"uid" comment:"兑换用户ID"`
	RedeemedTimestamp int64  `json:"redeemedTimestamp" comment:"兑换时间"`
	RedeemedIP        string `gorm:"size:45" json:"redeemedIP" comment:"兑换IP地址"`

	// 创建信息
	CreatedBy   string `gorm:"size:50" json:"createdBy" comment:"创建者"`
	BatchId     string `gorm:"size:50;index" json:"batchId" comment:"批次ID"`
	Description string `gorm:"size:200" json:"description" comment:"描述"`
}

func (PromotionCode) TableName() string {
	return "promotion_codes"
}

// 兑换操作日志表 - 用于审计和安全监控
type RedeemLog struct {
	ModelAutoId
	// 基本信息
	Code string `gorm:"size:32;index" json:"code" comment:"兑换码"`
	Uid  string `gorm:"index" json:"uid" comment:"用户ID"`

	// 操作信息
	Action       string `gorm:"size:20" json:"action" comment:"操作类型：redeem,create,disable"`
	Status       string `gorm:"size:20" json:"status" comment:"操作结果：success,failed"`
	ErrorMessage string `gorm:"size:500" json:"errorMessage" comment:"错误信息"`

	// 网络信息
	ClientIP  string `gorm:"size:45;index" json:"clientIP" comment:"客户端IP"`
	UserAgent string `gorm:"size:500" json:"userAgent" comment:"用户代理"`

	// 奖励信息
	RedeemType    string `gorm:"size:20" json:"redeemType" comment:"兑换类型"`
	RewardDetails string `gorm:"type:json" json:"rewardDetails" comment:"奖励详情JSON"`

	// 时间戳
	Timestamp int64 `gorm:"index" json:"timestamp" comment:"操作时间戳"`
}

func (RedeemLog) TableName() string {
	return "redeem_logs"
}
func (r *VipModel) CreatePayOrder(uid string, productId uint, platform string) (string, error) {
	terminal := 1
	switch platform {
	case util.PlatformIOS:
		terminal = 2
	case util.PlatformAndroid:
		terminal = 1
	}
	query := TradeProduct{ModelAutoId: ModelAutoId{Id: productId}, Terminal: terminal}
	result := TradeProduct{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return "", err
		}
		return "", nil
	}
	//创建订单信息
	newUUID := uuid.NewString()
	saver := UserPurchaseOrder{
		OrderNo:         newUUID,
		Uid:             uid,
		ProductID:       result.Id,
		ProductName:     result.Name,
		ProductType:     result.Type,
		Currency:        result.Currency,
		Amount:          result.Price,
		PaymentProvider: 1,
	}
	err := r.SaveOne(&saver)
	if err != nil {
		return "", err
	}
	return newUUID, nil
}

// AddPromotionCode 创建VIP兑换码（向后兼容）
func (r *VipModel) AddPromotionCode(days int, vipLevel int) (string, error) {
	return r.CreateRedeemCode(CreateRedeemCodeReq{
		RedeemType:  RedeemTypeVIP,
		Days:        days,
		VipLevel:    vipLevel,
		MaxUseCount: 1,
		ExpiresAt:   time.Now().AddDate(0, 6, 0).Unix(), // 6个月过期
		Description: fmt.Sprintf("VIP%d级别%d天", vipLevel, days),
		CreatedBy:   "system",
	})
}

// CreateRedeemCodeReq 创建兑换码请求
type CreateRedeemCodeReq struct {
	RedeemType   string                 `json:"redeemType"`   // 兑换类型
	RewardConfig map[string]interface{} `json:"rewardConfig"` // 奖励配置
	Days         int                    `json:"days"`         // VIP天数（向后兼容）
	VipLevel     int                    `json:"vipLevel"`     // VIP等级（向后兼容）
	MaxUseCount  int                    `json:"maxUseCount"`  // 最大使用次数
	ExpiresAt    int64                  `json:"expiresAt"`    // 过期时间
	Description  string                 `json:"description"`  // 描述
	CreatedBy    string                 `json:"createdBy"`    // 创建者
	BatchId      string                 `json:"batchId"`      // 批次ID
}

// CreateRedeemCode 创建兑换码
func (r *VipModel) CreateRedeemCode(req CreateRedeemCodeReq) (string, error) {
	// 生成安全的兑换码
	secretKey := r.config.RedeemCode.SecretKey
	if secretKey == "" {
		secretKey = "default-secret-key-change-in-production"
	}
	generator := security.NewRedeemCodeGenerator(secretKey)
	code, hash, err := generator.GenerateSecureCode()
	if err != nil {
		return "", fmt.Errorf("failed to generate redeem code: %w", err)
	}

	// 序列化奖励配置
	rewardConfigJSON := ""
	if req.RewardConfig != nil {
		configBytes, err := json.Marshal(req.RewardConfig)
		if err != nil {
			return "", fmt.Errorf("failed to marshal reward config: %w", err)
		}
		rewardConfigJSON = string(configBytes)
	}

	// 创建兑换码记录
	redeemCode := PromotionCode{
		Code:         code,
		CodeHash:     hash,
		Status:       RedeemStatusUnused,
		RedeemType:   req.RedeemType,
		RewardConfig: rewardConfigJSON,
		Days:         req.Days,
		VipLevel:     req.VipLevel,
		MaxUseCount:  req.MaxUseCount,
		UsedCount:    0,
		ExpiresAt:    req.ExpiresAt,
		CreatedBy:    req.CreatedBy,
		BatchId:      req.BatchId,
		Description:  req.Description,
	}

	err = r.SaveOne(&redeemCode)
	if err != nil {
		return "", fmt.Errorf("failed to save redeem code: %w", err)
	}

	// 记录创建日志
	r.logRedeemAction("create", code, "", "success", "", req.RedeemType, rewardConfigJSON, "")

	return code, nil
}

// logRedeemAction 记录兑换操作日志
func (r *VipModel) logRedeemAction(action, code, uid, status, errorMsg, redeemType, rewardDetails, userAgent string) {
	r.logRedeemActionWithIP(action, code, uid, status, errorMsg, redeemType, rewardDetails, userAgent, "")
}

// logRedeemActionWithIP 记录兑换操作日志（包含IP）
func (r *VipModel) logRedeemActionWithIP(action, code, uid, status, errorMsg, redeemType, rewardDetails, userAgent, clientIP string) {
	log := RedeemLog{
		Code:          code,
		Uid:           uid,
		Action:        action,
		Status:        status,
		ErrorMessage:  errorMsg,
		ClientIP:      clientIP,
		UserAgent:     userAgent,
		RedeemType:    redeemType,
		RewardDetails: rewardDetails,
		Timestamp:     time.Now().Unix(),
	}

	// 异步保存日志，不影响主流程
	go func() {
		if err := r.SaveOne(&log); err != nil {
			logrus.WithError(err).Error("Failed to save redeem log")
		}
	}()
}
func (r *VipModel) ExchangeCode(c *gin.Context, uid string, code string) error {
	if uid == "" {
		return errors.New("用户为空")
	}

	clientIP := c.ClientIP()
	userAgent := c.Request.UserAgent()

	// 验证兑换码格式
	secretKey := r.config.RedeemCode.SecretKey
	if secretKey == "" {
		secretKey = "default-secret-key-change-in-production"
	}
	generator := security.NewRedeemCodeGenerator(secretKey)
	if !generator.ValidateCode(code) {
		r.logRedeemActionWithIP("redeem", code, uid, "failed", "兑换码格式无效", "", "", userAgent, clientIP)
		return errors.New("兑换码格式无效")
	}

	redeemCode := PromotionCode{}
	terminal := util.GetTerminal(c)

	// 查找兑换码
	if found, _ := r.GetOne(&redeemCode, PromotionCode{Code: code}); !found {
		r.logRedeemActionWithIP("redeem", code, uid, "failed", "找不到兑换码", "", "", userAgent, clientIP)
		return errors.New("找不到兑换码")
	}

	// 验证用户存在
	if found, _ := r.GetOne(&User{}, User{Model: Model{Id: uid}}); !found {
		r.logRedeemActionWithIP("redeem", code, uid, "failed", "找不到用户", redeemCode.RedeemType, "", userAgent, clientIP)
		return errors.New("找不到用户")
	}

	// 检查兑换码状态
	if redeemCode.Status != RedeemStatusUnused {
		var statusMsg string
		switch redeemCode.Status {
		case RedeemStatusUsed:
			statusMsg = "兑换码已被使用"
		case RedeemStatusExpired:
			statusMsg = "兑换码已过期"
		case RedeemStatusDisabled:
			statusMsg = "兑换码已被禁用"
		default:
			statusMsg = "兑换码状态异常"
		}
		r.logRedeemActionWithIP("redeem", code, uid, "failed", statusMsg, redeemCode.RedeemType, "", userAgent, clientIP)
		return errors.New(statusMsg)
	}

	// 检查过期时间
	if redeemCode.ExpiresAt > 0 && time.Now().Unix() > redeemCode.ExpiresAt {
		// 更新状态为已过期
		r.Update(&PromotionCode{Status: RedeemStatusExpired}, "id = ?", redeemCode.Id)
		r.logRedeemActionWithIP("redeem", code, uid, "failed", "兑换码已过期", redeemCode.RedeemType, "", userAgent, clientIP)
		return errors.New("兑换码已过期")
	}

	// 检查使用次数
	if redeemCode.UsedCount >= redeemCode.MaxUseCount {
		r.logRedeemActionWithIP("redeem", code, uid, "failed", "兑换码使用次数已达上限", redeemCode.RedeemType, "", userAgent, clientIP)
		return errors.New("兑换码使用次数已达上限")
	}

	// 开始事务处理兑换
	err := r.Tx(
		// 1. 根据兑换类型处理奖励
		func(txDb *dbx.DBExtension) error {
			return r.processRedeemReward(txDb, &redeemCode, uid, terminal)
		},
		// 2. 更新兑换码状态
		func(txDb *dbx.DBExtension) error {
			redeemCode.Uid = uid
			redeemCode.UsedCount++
			redeemCode.RedeemedTimestamp = time.Now().Unix()
			redeemCode.RedeemedIP = clientIP

			// 如果达到最大使用次数，标记为已使用
			if redeemCode.UsedCount >= redeemCode.MaxUseCount {
				redeemCode.Status = RedeemStatusUsed
			}

			if err := txDb.Update(&redeemCode, "id = ?", redeemCode.Id); err != nil {
				return err
			}
			return nil
		},
	)

	if err != nil {
		r.logRedeemActionWithIP("redeem", code, uid, "failed", err.Error(), redeemCode.RedeemType, "", userAgent, clientIP)
		return err
	}

	// 记录成功日志
	rewardDetails, _ := json.Marshal(map[string]interface{}{
		"redeemType": redeemCode.RedeemType,
		"days":       redeemCode.Days,
		"vipLevel":   redeemCode.VipLevel,
		"config":     redeemCode.RewardConfig,
	})
	r.logRedeemActionWithIP("redeem", code, uid, "success", "", redeemCode.RedeemType, string(rewardDetails), userAgent, clientIP)

	return nil
}

// processRedeemReward 处理兑换奖励
func (r *VipModel) processRedeemReward(txDb *dbx.DBExtension, redeemCode *PromotionCode, uid string, terminal int) error {
	switch redeemCode.RedeemType {
	case RedeemTypeVIP:
		return r.processVIPReward(txDb, redeemCode, uid, terminal)
	case RedeemTypePoints:
		return r.processPointsReward(txDb, redeemCode, uid)
	case RedeemTypeCoupon:
		return r.processCouponReward(txDb, redeemCode, uid)
	case RedeemTypeContent:
		return r.processContentReward(txDb, redeemCode, uid)
	default:
		return fmt.Errorf("unsupported redeem type: %s", redeemCode.RedeemType)
	}
}

// processVIPReward 处理VIP奖励
func (r *VipModel) processVIPReward(txDb *dbx.DBExtension, redeemCode *PromotionCode, uid string, terminal int) error {
	// 创建VIP流水记录
	userVIPFlow := UserVIPFlow{
		Uid:                uid,
		Title:              fmt.Sprintf("兑换码核销，使用者：%s 兑换码：%s", uid, redeemCode.Code),
		Type:               2,
		Operation:          1,
		OperationTimestamp: timex.Now().UnixMilli(),
		Days:               redeemCode.Days,
		Terminal:           terminal,
		BizID:              redeemCode.Code,
	}

	if err := txDb.SaveOne(&userVIPFlow); err != nil {
		return fmt.Errorf("failed to save VIP flow: %w", err)
	}

	// 更新用户VIP关系
	var userVIPRelations UserVIPRelations
	found, err := txDb.GetOne(&userVIPRelations, UserVIPRelations{Uid: uid})
	if err != nil {
		return fmt.Errorf("failed to get user VIP relations: %w", err)
	}

	// 统计用户会员总天数
	days, err := r.sumDaysByUserVipFlow(txDb, uid)
	if err != nil {
		return fmt.Errorf("failed to sum VIP days: %w", err)
	}

	expireTimestamp := timex.Now().UnixMilli() + int64(days)*24*60*60*1000

	if !found {
		// 创建新的VIP关系
		userVIPRelations = UserVIPRelations{
			Uid:             uid,
			IsVip:           1,
			VipID:           r.getVipIDByLevel(redeemCode.VipLevel),
			IsSubscription:  0,
			ExpireDate:      util.TsFormat2String(expireTimestamp),
			ExpireTimestamp: expireTimestamp,
		}
		if err := txDb.SaveOne(&userVIPRelations); err != nil {
			return fmt.Errorf("failed to create VIP relations: %w", err)
		}
	} else {
		// 更新现有VIP关系
		userVIPRelations.ExpireDate = util.TsFormat2String(expireTimestamp)
		userVIPRelations.IsVip = 1
		userVIPRelations.VipID = r.getVipIDByLevel(redeemCode.VipLevel)
		userVIPRelations.ExpireTimestamp = expireTimestamp
		if err := txDb.Update(&userVIPRelations, "id = ?", userVIPRelations.Id); err != nil {
			return fmt.Errorf("failed to update VIP relations: %w", err)
		}
	}

	// 分配权益
	benefitModel := NewBenefitModel(r.DbModel, r.config)
	if err := benefitModel.AssignBenefitsByVipLevel(uid, redeemCode.VipLevel); err != nil {
		return fmt.Errorf("failed to assign benefits: %w", err)
	}

	return nil
}

// processPointsReward 处理积分奖励
func (r *VipModel) processPointsReward(txDb *dbx.DBExtension, redeemCode *PromotionCode, uid string) error {
	// TODO: 实现积分奖励逻辑
	// 这里可以根据RewardConfig中的配置给用户增加积分
	logrus.Infof("Processing points reward for user %s, code %s", uid, redeemCode.Code)
	return nil
}

// processCouponReward 处理优惠券奖励
func (r *VipModel) processCouponReward(txDb *dbx.DBExtension, redeemCode *PromotionCode, uid string) error {
	// TODO: 实现优惠券奖励逻辑
	// 这里可以根据RewardConfig中的配置给用户发放优惠券
	logrus.Infof("Processing coupon reward for user %s, code %s", uid, redeemCode.Code)
	return nil
}

// processContentReward 处理内容解锁奖励
func (r *VipModel) processContentReward(txDb *dbx.DBExtension, redeemCode *PromotionCode, uid string) error {
	// TODO: 实现内容解锁奖励逻辑
	// 这里可以根据RewardConfig中的配置为用户解锁特定内容
	logrus.Infof("Processing content reward for user %s, code %s", uid, redeemCode.Code)
	return nil
}

// CreateBatchRedeemCodes 批量创建兑换码
func (r *VipModel) CreateBatchRedeemCodes(req CreateRedeemCodeReq, count int) ([]string, error) {
	if count <= 0 || count > 1000 {
		return nil, fmt.Errorf("invalid count: %d, must be between 1 and 1000", count)
	}

	// 生成批次ID
	batchId := fmt.Sprintf("batch_%d_%s", time.Now().Unix(), util.GenerateSnowflakeID())
	req.BatchId = batchId

	// 生成兑换码
	secretKey := r.config.RedeemCode.SecretKey
	if secretKey == "" {
		secretKey = "default-secret-key-change-in-production"
	}
	generator := security.NewRedeemCodeGenerator(secretKey)
	codePairs, err := generator.GenerateBatchCodes(count)
	if err != nil {
		return nil, fmt.Errorf("failed to generate batch codes: %w", err)
	}

	// 序列化奖励配置
	rewardConfigJSON := ""
	if req.RewardConfig != nil {
		configBytes, err := json.Marshal(req.RewardConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal reward config: %w", err)
		}
		rewardConfigJSON = string(configBytes)
	}

	// 批量插入数据库
	codes := make([]string, 0, count)
	redeemCodes := make([]PromotionCode, 0, count)

	for _, pair := range codePairs {
		redeemCode := PromotionCode{
			Code:         pair.Code,
			CodeHash:     pair.Hash,
			Status:       RedeemStatusUnused,
			RedeemType:   req.RedeemType,
			RewardConfig: rewardConfigJSON,
			Days:         req.Days,
			VipLevel:     req.VipLevel,
			MaxUseCount:  req.MaxUseCount,
			UsedCount:    0,
			ExpiresAt:    req.ExpiresAt,
			CreatedBy:    req.CreatedBy,
			BatchId:      batchId,
			Description:  req.Description,
		}
		redeemCodes = append(redeemCodes, redeemCode)
		codes = append(codes, pair.Code)
	}

	// 使用事务批量插入
	err = r.Tx(func(txDb *dbx.DBExtension) error {
		for _, redeemCode := range redeemCodes {
			if err := txDb.SaveOne(&redeemCode); err != nil {
				return fmt.Errorf("failed to save redeem code %s: %w", redeemCode.Code, err)
			}
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to save batch codes: %w", err)
	}

	// 记录批量创建日志
	r.logRedeemAction("batch_create", batchId, "", "success",
		fmt.Sprintf("Created %d codes", count), req.RedeemType, rewardConfigJSON, "")

	return codes, nil
}

// GetRedeemCodesByBatch 根据批次ID获取兑换码列表
func (r *VipModel) GetRedeemCodesByBatch(batchId string) ([]PromotionCode, error) {
	var codes []PromotionCode
	err := r.GetList(&codes, PromotionCode{BatchId: batchId})
	if err != nil {
		return nil, fmt.Errorf("failed to get codes by batch: %w", err)
	}
	return codes, nil
}

// DisableRedeemCode 禁用兑换码
func (r *VipModel) DisableRedeemCode(code string, reason string) error {
	var redeemCode PromotionCode
	found, err := r.GetOne(&redeemCode, PromotionCode{Code: code})
	if err != nil {
		return fmt.Errorf("failed to get redeem code: %w", err)
	}
	if !found {
		return fmt.Errorf("redeem code not found: %s", code)
	}

	// 更新状态为禁用
	err = r.Update(&PromotionCode{Status: RedeemStatusDisabled}, "id = ?", redeemCode.Id)
	if err != nil {
		return fmt.Errorf("failed to disable redeem code: %w", err)
	}

	// 记录禁用日志
	r.logRedeemAction("disable", code, "", "success", reason, redeemCode.RedeemType, "", "")

	return nil
}

// 根据等级获取VIP ID
func (r *VipModel) getVipIDByLevel(level int) uint {
	var vip VIP
	found, _ := r.GetOne(&vip, VIP{Level: level})
	if !found {
		// 如果找不到对应等级，返回默认等级
		return r.getProVipID()
	}
	return vip.Id
}

// 添加一个辅助方法来获取PRO会员的VipID
func (r *VipModel) getProVipID() uint {
	var vip VIP
	found, _ := r.GetOne(&vip, VIP{Level: VIP_LEVEL_PRO})
	if !found {
		return 0
	}
	return vip.Id
}

// transactionId
// 定义: 这是每个单独交易的唯一标识符。
// 用途: 用于标识特定的购买交易。
// 变化: 每次新的购买或续订都会生成一个新的 transactionId。
// originalTransactionId
// 定义: 这是首次购买订阅或可续订商品时生成的交易标识符。
// 用途: 用于标识原始购买交易。
// 变化: 对于自动续订的订阅，所有续订交易都会共享同一个 originalTransactionId。
// 使用场景
// transactionId: 用于跟踪和验证每次具体的交易。
// originalTransactionId: 用于追溯订阅的初始购买，特别是在处理续订或恢复购买时。
func (r *VipModel) ReceiveApplePayResult(notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) error {
	logrus.Info("ReceiveApplePayResult NotificationType=", notificationV2Payload.NotificationType, "Subtype=", notificationV2Payload.Subtype)
	tradeProduct := TradeProduct{}
	if found, err := r.GetOne(&tradeProduct, TradeProduct{Terminal: 2, IosProductId: renewalInfo.ProductId}); !found {
		if err != nil {
			return err
		}
		logrus.Error("ReceiveApplePayResult not found tartget tradeProduct IosProductId=", transactionInfo.ProductId)
		return errors.New("not found TradeProduct")
	}
	vipData := VIP{}
	if found, err := r.GetOne(&vipData, VIP{ModelAutoId: ModelAutoId{Id: tradeProduct.VipID}}); !found {
		if err != nil {
			return err
		}
		logrus.Error("ReceiveApplePayResult not found tartget vip id=", tradeProduct.VipID)
		return errors.New("not found VIP")
	}
	userPurchaseOrder := UserPurchaseOrder{}
	found, err := r.GetOne(&userPurchaseOrder, UserPurchaseOrder{OrderNo: transactionInfo.AppAccountToken, PaymentProvider: 1})
	if err != nil {
		return err
	}
	fmt.Printf("tradeOrder: %v\n", userPurchaseOrder)
	fmt.Printf("found: %v\n", found)
	if !found {
		logrus.Info("ReceiveApplePayResult appAccountToken is empty , transactionId=", transactionInfo.TransactionId)
		//如果 AppAccountToken 为空，说明是用户自己不在app操作订阅 根据OriginalTransactionId来获取
		if found, err := r.GetOne(&userPurchaseOrder, UserPurchaseOrder{OutTransactionId: transactionInfo.OriginalTransactionId, PaymentProvider: 1}); !found {
			if err != nil {
				return err
			}
			logrus.Error("ReceiveApplePayResult not found tartget tradeOrder uid=", userPurchaseOrder.Uid)
			return errors.New("not found order")
		}
	}

	handler := r.selectHandler(notificationV2Payload)
	err = handler.Handle(r, tradeProduct, vipData, userPurchaseOrder, notificationV2Payload, renewalInfo, transactionInfo)
	if err != nil {
		userPurchaseOrder.Status = 3
		userPurchaseOrder.PaymentFailureReason = err.Error()
		userPurchaseOrder.FailureTimestamp = timex.Now().UnixMilli()
		r.Update(userPurchaseOrder, " id =?", userPurchaseOrder.Id)
	}
	return err
}
