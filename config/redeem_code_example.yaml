# 兑换码系统配置示例
# 请根据实际需求修改配置值

RedeemCode:
  # 兑换码生成密钥 - 生产环境中请使用强随机密钥
  SecretKey: "your-super-secret-key-for-redeem-code-generation-change-this-in-production"
  
  # 用户每日兑换限制
  UserDailyLimit: 5
  
  # IP每日兑换限制
  IPDailyLimit: 20
  
  # 用户每小时兑换限制
  UserHourlyLimit: 2
  
  # IP每小时兑换限制
  IPHourlyLimit: 10

# 完整配置示例（包含其他必要配置）
Env: "dev"
BaseUrl: "http://localhost:8080"
Port: "8080"
LogPath: "./logs"
IpDataPath: "./data/ip.dat"

Server:
  RunMode: "debug"
  HttpPort: 8080
  ReadTimeout: 60
  WriteTimeout: 60

AI:
  MaxResourcesPerBatch: 10

Redis:
  Host: "localhost:6379"
  Password: ""
  MaxIdle: 30
  MaxActive: 30
  IdleTimeout: 200
  DialTimeout: 10
  ReadTimeout: 10
  WriteTimeout: 10
  PoolTimeout: 10

DB:
  Url: "user:password@tcp(localhost:3306)/database?charset=utf8mb4&parseTime=True&loc=Local"
  MaxIdleConns: 10
  MaxOpenConns: 100
  ConnMaxIdleTimeSeconds: 300
  ConnMaxLifetimeSeconds: 3600

Jwt:
  SignKey: "your-jwt-sign-key"
  ExpireSeconds: 86400

JwtAdmin:
  SignKey: "your-admin-jwt-sign-key"
  ExpireSeconds: 86400

Limit:
  EnableIpLimit: true
  EnableUserLimit: true

Setting:
  Language: "zh-CN"
  AliyunOss:
    Host: "your-oss-host"
    Bucket: "your-bucket"
    Endpoint: "your-endpoint"
    AccessId: "your-access-id"
    AccessSecret: "your-access-secret"
    Region: "your-region"
